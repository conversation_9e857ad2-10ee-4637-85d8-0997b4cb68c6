<template>
    <el-dialog
    :model="props.visible"
    :before-close="handleClose"
  >
    <el-row>
        <el-col :span="12">
            <el-form>
                <el-form-item label="站点名称:" prop="name">
                    <el-input v-model="form.name" placeholder="请输入站点名称"></el-input>
                </el-form-item>
                <el-form-item label="站点ID:" prop="name">
                    <el-input v-model="form.name" placeholder="请输入站点名称"></el-input>
                </el-form-item>
                <el-form-item label="所属城市:" prop="name">
                    <el-input v-model="form.name" placeholder="请输入站点名称"></el-input>
                </el-form-item>
                <el-form-item label="快充数:" prop="name">
                    <el-input v-model="form.name" placeholder="请输入站点名称"></el-input>
                </el-form-item>
                <el-form-item label="慢充数:" prop="name">
                    <el-input v-model="form.name" placeholder="请输入站点名称"></el-input>
                </el-form-item>
            </el-form>
        </el-col>
        <el-col :span="12">
            <el-form>
                <el-form-item label="充电站状态:" prop="name">
                    <el-input v-model="form.name" placeholder="请输入站点名称"></el-input>
                </el-form-item>
                <el-form-item label="正在充电:" prop="name">
                    <el-input v-model="form.name" placeholder="请输入站点名称"></el-input>
                </el-form-item>
                <el-form-item label="充电故障:" prop="name">
                    <el-input v-model="form.name" placeholder="请输入站点名称"></el-input>
                </el-form-item>
                <el-form-item label="站点负责人:" prop="name">
                    <el-input v-model="form.name" placeholder="请输入站点名称"></el-input>
                </el-form-item>
                <el-form-item label="负责人电话:" prop="name">
                    <el-input v-model="form.name" placeholder="请输入站点名称"></el-input>
                </el-form-item>
            </el-form>


        </el-col>
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import type {RowType} from "@/type/RowType"

const props=defineProps({
    visible:{
        type:Boolean,
        require:true
    }
})

const form=reactive<RowType>({
    name:"",
    id:"",
    city:"",
    fast:0,
    slow:0,
    status:0,
    now:0,
    fault:0,
    person:"",
    tel:0
})

const emit=defineEmits(["close"])

const handleClose=()=>{
    emit("close")
}

const handleSave=()=>{
    console.log("保存")
    emit("close")
}
</script>